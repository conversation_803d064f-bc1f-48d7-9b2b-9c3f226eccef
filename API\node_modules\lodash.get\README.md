# lodash.get v4.4.2

The [lodash](https://lodash.com/) method `_.get` exported as a [Node.js](https://nodejs.org/) module.

## Installation

Using npm:
```bash
$ {sudo -H} npm i -g npm
$ npm i --save lodash.get
```

In Node.js:
```js
var get = require('lodash.get');
```

See the [documentation](https://lodash.com/docs#get) or [package source](https://github.com/lodash/lodash/blob/4.4.2-npm-packages/lodash.get) for more details.
